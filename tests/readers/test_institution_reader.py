import pytest
from pathlib import Path
from pinnacle_io.models import Institution
from pinnacle_io.readers.institution_reader import InstitutionReader
from pinnacle_io.services.file_reader import <PERSON><PERSON><PERSON>er


def test_read_institution_file():
    """Tests reading a valid Institution file using direct path (backward compatibility)."""
    institution = InstitutionReader.read(institution_path=str(Path(__file__).parent.parent / "test_data/01"))

    assert isinstance(institution, Institution)
    assert institution.institution_id == 1
    assert institution.name == "16.2 Smart Enterprise"
    assert institution.institution_path == "Institution_1"
    assert institution.default_mount_point == "Mount_0"

    # Check PatientLiteList
    assert len(institution.patient_lite_list) >= 1
    patient_lite = institution.patient_lite_list[0]
    assert patient_lite.patient_id == 1
    assert patient_lite.patient_path == "Institution_1/Mount_0/Patient_1"
    assert patient_lite.mount_point == "Mount_0"
    assert patient_lite.last_name == "LAST"
    assert patient_lite.first_name == "FIRST"
    assert patient_lite.middle_name == "M"
    assert patient_lite.medical_record_number == "000000"
    assert patient_lite.physician == "TEST,MD"
    assert patient_lite.last_modified.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert patient_lite.dir_size == 750.131
    assert patient_lite.institution.institution_id == institution.institution_id

    # Check ObjectVersion
    assert institution.write_version == "Launch Pad: 16.2"
    assert institution.create_version == "Launch Pad: 16.0"
    assert institution.version_login_name == "candor01"
    assert institution.create_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert institution.write_time_stamp.strftime("%Y-%m-%d %H:%M:%S") == "2020-01-01 10:00:00"
    assert institution.last_modified_time_stamp.strftime("%Y-%m-%d.%H:%M:%S") == "2020-01-01.10:00:00"


def test_read_institution_file_with_service():
    """Tests reading a valid Institution file using file service."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_dir))
    institution = InstitutionReader.read(file_service=file_service)

    assert isinstance(institution, Institution)
    assert institution.institution_id == 1
    assert institution.name == "16.2 Smart Enterprise"
    assert institution.institution_path == "Institution_1"
    assert institution.default_mount_point == "Mount_0"
