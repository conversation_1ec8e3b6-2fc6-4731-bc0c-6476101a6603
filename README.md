# pinnacle_io

`pinnacle_io` is a Python library for reading and writing Pinnacle radiotherapy treatment planning files using structured SQLAlchemy models. It provides a unified interface for accessing Pinnacle data from directories, tar files, zip files, and in-memory archives.

## Features

- **Unified API**: Single interface for reading from directories, tar files, and zip files
- **Multiple Data Types**: Support for Institution, Patient, Plan, ImageSet, Trial, ROI, Dose, and more
- **SQLAlchemy Models**: Structured data models for easy manipulation and validation
- **Flexible File Access**: Read from directories, compressed archives, or in-memory data
- **Auto-Detection**: Automatically detect and read appropriate data types
- **Comprehensive Testing**: Fully tested with pytest including edge cases and error handling
- **Type Safe**: Full type hints and static analysis support

## Installation

```bash
git clone https://your.repo.url/pinnacle_io
cd pinnacle_io
pip install -e .
```

## Quick Start

### Pinnacle Treatment Planning Data

Pinnacle saves treatment planning data in text and binary files in nested directories. Given
a root path to the top level Pinnacle directory, sample files include:

/root_path/Institution
/root_path/Institution_#/Mount_0/Patient_#/Patient
/root_path/Institution_#/Mount_0/Patient_#/ImageSet_#.img
/root_path/Institution_#/Mount_0/Patient_#/ImageSet_#.header
/root_path/Institution_#/Mount_0/Patient_#/ImageSet_#.ImageInfo
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.roi
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Points
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial.binary.###
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.PatientSetup
/root_path/Institution_#/Mount_0/Patient_#/Plan_#/plan.Pinnacle.Machines

In summary, an Institution file is loaded from the Pinnacle root path, the Patient file
and image set files are loaded from the patient folder, and the plan files are loaded from
the plan folder.

### Basic Usage (Recommended)

```python
from pinnacle_io import PinnacleReader

# Initialize a Pinnacle reader using the path to an archive (*.tar/*.tar.gz/*.tgz/*.zip)
# or the path to data extracted from an archive (the path containing the Institution file)
reader = PinnacleReader("/path/to/pinnacle/data")
institution = reader.get_institution()
print("Loading patients from institution:", institution.name)
for patient_lite in institution.patient_lite_list:
    patient = reader.get_patient(patient_lite.patient_path)
    print(patient.last_and_first_name)
```

### Using Reader Services

```python
from pinnacle_io import FileReader, TarFileReader, ZipFileReader

# Directory reader
file_reader = FileReader("/path/to/pinnacle/data")
institution = file_reader.get_institution()
patient = file_reader.get_patient()
trials = file_reader.get_trials()

# Tar reader
tar_reader = TarFileReader("/path/to/data.tar.gz")
institution = tar_reader.get_institution()

# Zip reader  
zip_reader = ZipFileReader("/path/to/data.zip")
institution = zip_reader.get_institution()
```

### Working with Models

```python
from pinnacle_io import PinnacleReader
from pinnacle_io.models import Institution, Patient, Trial

# Read data using PinnacleReader (recommended)
reader = PinnacleReader("/path/to/pinnacle/data")
institution = reader.get_institution()

# Access model properties
print(f"Institution: {institution.name}")
print(f"Address: {institution.address}")

# Read patient data
patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
print(f"Patient: {patient.first_name} {patient.last_name}")
print(f"MRN: {patient.medical_record_number}")

# Work with trial lists
trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
for trial in trials:
    print(f"Trial: {trial.name}, Prescription: {trial.prescription_dose}")
```

### Deprecated Functions

The `pinnacle_io.read()` and `pinnacle_io.write()` functions are deprecated and will be removed in a future version. Use the `PinnacleReader` class instead for better path management and cleaner API.

<details>
<summary>Deprecated Usage (click to expand)</summary>

```python
import pinnacle_io

# DEPRECATED: These functions will be removed in a future version
# institution = pinnacle_io.read("/path/to/data", "Institution")
# pinnacle_io.write("/path/to/output", institution)  # Not implemented

# RECOMMENDED: Use PinnacleReader instead
reader = PinnacleReader("/path/to/pinnacle/data")
institution = reader.get_institution()
```

</details>

### Error Handling

```python
from pinnacle_io import PinnacleReader

try:
    reader = PinnacleReader("/path/to/pinnacle/data")
    institution = reader.get_institution()
except FileNotFoundError:
    print("Data path not found")
except ValueError as e:
    print(f"Unsupported file format: {e}")
except Exception as e:
    print(f"Error reading data: {e}")

# Access specific data with error handling
try:
    patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
    trials = reader.get_trials("Institution_1/Mount_0/Patient_1/Plan_1")
except Exception as e:
    print(f"Error accessing patient data: {e}")
```

## Data Types and File Mappings

| File Pattern | Data Object | Description |
|--------------|-------------|-------------|
| `Institution` | `Institution` | Root institution information |
| `Patient` | `Patient` | Patient demographics and medical info |
| `ImageSet_#.header` | `ImageSet` | Medical imaging data sets |
| `ImageSet_#.ImageInfo` | `ImageInfo` | Imaging metadata |
| `plan.Trial` | `List[Trial]` | Treatment trial definitions |
| `plan.roi` | `List[ROI]` | Regions of interest |
| `plan.Points` | `List[Point]` | Treatment planning points |
| `plan.PatientSetup` | `PatientSetup` | Patient positioning |
| `plan.Pinnacle.Machines` | `List[Machine]` | Treatment machines |
| `plan.Trial.binary.###` | `Dose` | Dose distribution data |

## Development

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=pinnacle_io --cov-report=html

# Run specific test categories
pytest tests/test_api.py          # API tests
pytest tests/test_integration.py  # Integration tests
pytest tests/test_round_trip.py   # Round-trip tests
pytest tests/services/            # Service tests
```

### Code Quality

```bash
# Format code
black pinnacle_io/ tests/

# Sort imports
isort pinnacle_io/ tests/

# Type checking
mypy pinnacle_io/
pyright
```

