"""
Basic usage examples for pinnacle_io library.

This module demonstrates the fundamental usage patterns for reading
Pinnacle treatment planning data using the PinnacleReader class.
"""

from pinnacle_io import PinnacleReader
from pinnacle_io.models import Institution, Patient, Trial, ROI, Dose

def basic_reader_usage():
    """Demonstrate basic PinnacleReader usage."""
    
    # Initialize reader with path to Pinnacle data
    # This works with directories, tar files, or zip files
    reader = PinnacleReader("/path/to/pinnacle/data")
    
    # Read institution data (root level)
    institution = reader.get_institution()
    print(f"Institution: {institution.name}")
    print(f"Address: {institution.address}")
    
    # List available patients
    print("\nAvailable patients:")
    for patient_lite in institution.patient_lite_list:
        print(f"  - {patient_lite.patient_path}")
    
    return reader, institution

def read_patient_data(reader: PinnacleReader):
    """Demonstrate reading patient data."""
    
    # Read patient data from first available patient
    institution = reader.get_institution()
    if institution.patient_lite_list:
        patient_path = institution.patient_lite_list[0].patient_path
        patient = reader.get_patient(patient_path)
        
        print(f"\nPatient Details:")
        print(f"Name: {patient.first_name} {patient.last_name}")
        print(f"MRN: {patient.medical_record_number}")
        print(f"Birth Date: {patient.birth_date}")
        print(f"Sex: {patient.sex}")
        
        return patient
    
    return None

def read_imaging_data(reader: PinnacleReader):
    """Demonstrate reading imaging data."""
    
    # Read image set data
    try:
        image_set = reader.get_image_set("Institution_1/Mount_0/Patient_1")
        print(f"\nImage Set:")
        print(f"Number of images: {len(image_set.images) if image_set.images else 0}")
        print(f"Image dimensions: {image_set.dimension_x}x{image_set.dimension_y}x{image_set.dimension_z}")
        
        # Read image info
        image_info_list = reader.get_image_info("Institution_1/Mount_0/Patient_1")
        print(f"Image info entries: {len(image_info_list)}")
        
        return image_set, image_info_list
    except Exception as e:
        print(f"Error reading imaging data: {e}")
        return None, None

def read_plan_data(reader: PinnacleReader):
    """Demonstrate reading treatment plan data."""
    
    plan_path = "Institution_1/Mount_0/Patient_1/Plan_1"
    
    try:
        # Read trials
        trials = reader.get_trials(plan_path)
        print(f"\nTreatment Trials: {len(trials)}")
        for trial in trials:
            print(f"  - {trial.name}: {trial.prescription_dose} cGy")
        
        # Read ROIs (regions of interest)
        rois = reader.get_rois(plan_path)
        print(f"\nROIs: {len(rois)}")
        for roi in rois:
            print(f"  - {roi.name}: {roi.type}")
        
        # Read treatment points
        points = reader.get_points(plan_path)
        print(f"\nTreatment Points: {len(points)}")
        
        # Read patient setup
        patient_setup = reader.get_patient_setup(plan_path)
        print(f"\nPatient Setup: {patient_setup.couch_angle}° couch angle")
        
        # Read machines
        machines = reader.get_machines(plan_path)
        print(f"\nTreatment Machines: {len(machines)}")
        for machine in machines:
            print(f"  - {machine.name}: {machine.energy} MV")
        
        return trials, rois, points, patient_setup, machines
    except Exception as e:
        print(f"Error reading plan data: {e}")
        return None, None, None, None, None

def read_dose_data(reader: PinnacleReader):
    """Demonstrate reading dose data."""
    
    plan_path = "Institution_1/Mount_0/Patient_1/Plan_1"
    
    try:
        # Read dose distribution
        dose = reader.get_dose(plan_path)
        print(f"\nDose Distribution:")
        print(f"Grid size: {dose.dimension_x}x{dose.dimension_y}x{dose.dimension_z}")
        print(f"Voxel size: {dose.pixel_spacing_x}x{dose.pixel_spacing_y}x{dose.slice_spacing}")
        print(f"Max dose: {dose.max_dose} cGy")
        
        return dose
    except Exception as e:
        print(f"Error reading dose data: {e}")
        return None

def main():
    """Main example function."""
    
    print("Pinnacle IO Basic Usage Examples")
    print("=" * 40)
    
    try:
        # Basic usage
        reader, institution = basic_reader_usage()
        
        # Read patient data
        patient = read_patient_data(reader)
        
        # Read imaging data
        image_set, image_info = read_imaging_data(reader)
        
        # Read plan data
        trials, rois, points, setup, machines = read_plan_data(reader)
        
        # Read dose data
        dose = read_dose_data(reader)
        
        print("\n" + "=" * 40)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error in main execution: {e}")
        print("Make sure you have valid Pinnacle data at the specified path.")

if __name__ == "__main__":
    main()