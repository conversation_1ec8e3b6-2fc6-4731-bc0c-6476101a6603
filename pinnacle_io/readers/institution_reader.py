"""
Reader for Pinnacle Institution files.
"""

from typing import Any
from pinnacle_io.models import Institution
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class InstitutionReader(BasePinnacleReader):
    """
    Reader for Pinnacle Institution files.
    """

    @staticmethod
    def read(institution_path: str | None = None, file_service: Any = None) -> Institution:
        """
        Read a Pinnacle Institution file and create an Institution model.

        Args:
            institution_path: Path to the Institution file or directory
            file_service: File service object with open_file method

        Returns:
            Institution model populated with data from the files

        Usage:
            institution = InstitutionReader.read("/path/to/Institution_0/Institution")
            institution = InstitutionReader.read("/path/to/Institution_0")
            institution = InstitutionReader.read("/path/to/Institution_0", file_service=file_service)
        """
        # Handle special case for empty or "Institution" paths
        institution_path = institution_path or ""
        if institution_path == "Institution":
            file_path, file_name = ".", "Institution"
        else:
            file_path, file_name = InstitutionReader._resolve_file_path(institution_path, "Institution")
        
        # Read file content using base class utility
        content_lines = InstitutionReader._read_file_lines(file_path, file_name, file_service)
        
        return InstitutionReader.parse(content_lines)

    @staticmethod
    def parse(content_lines: list[str]) -> Institution:
        """
        Parse a Pinnacle Institution content string and create an Institution model.

        Args:
            content_lines: Pinnacle Institution content lines

        Returns:
            Institution model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        institution = Institution(**data)
        return institution
