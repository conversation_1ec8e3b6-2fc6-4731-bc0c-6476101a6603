"""
Reader for Pinnacle ImageSet files.
"""

import os
import re
import numpy as np
import numpy.typing as npt
from typing import Any, Dict, Tuple
from pinnacle_io.models import ImageSet
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.image_info_reader import ImageInfoReader
from pinnacle_io.readers.base_pinnacle_reader import BasePinnacleReader


class ImageSetReader(BasePinnacleReader):
    """
    Reader for Pinnacle ImageSet files.

    This class provides methods for reading ImageSet files and creating models from the data in the files.
    """

    # Class-level cache for image dimensions to avoid repeated header parsing
    _dimension_cache: Dict[str, Tuple[int, int, int]] = {}

    @staticmethod
    def read_from_ids(
        institution_id: int, 
        patient_id: int, 
        image_set_id: int, 
        mount_id: int = 0, 
        file_service: Any = None
    ) -> ImageSet:
        """
        Read a Pinnacle ImageSet file using institution, patient, and image set IDs.

        Args:
            institution_id: Institution ID number
            patient_id: Patient ID number
            image_set_id: ImageSet ID number (e.g., 0 for ImageSet_0)
            mount_id: Mount ID number (default: 0)
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Usage:
            imageset = ImageSetReader.read_from_ids(1, 5, 0)
            imageset = ImageSetReader.read_from_ids(1, 5, 0, mount_id=2)
            imageset = ImageSetReader.read_from_ids(1, 5, 0, file_service=file_service)
        """
        # Construct standard Pinnacle path format
        image_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"
        
        # Delegate to path-based method with image_index
        return ImageSetReader.read_from_path(image_path, image_index=image_set_id, file_service=file_service)

    @staticmethod
    def read_from_path(image_path: str, image_index: int | None = None, file_service: Any = None) -> ImageSet:
        """
        Read a Pinnacle ImageSet file from a specified path.

        This method determines whether the provided path is for a binary image file (.img)
        or a header file (.header), and delegates to the appropriate reader. The ImageInfoList
        is also read from the corresponding ImageInfo file.

        Args:
            image_path: Path to ImageSet_# file (either .img or .header extension is required)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Example:
            >>> image_set = ImageSetReader.read_from_path("/path/to/ImageSet_0.img")
            >>> image_set = ImageSetReader.read_from_path("/path/to/ImageSet_0.header")
            >>> image_set = ImageSetReader.read_from_path("/path/to/Patient", image_index=0)
        """
        if image_path.endswith(".img"):
            return ImageSetReader.read_image_set(image_path, image_index, file_service)

        # If not specified as an img, assume it's a header file
        return ImageSetReader.read_header(image_path, image_index, file_service)

    @staticmethod
    def read_header(image_header_path: str, image_index: int | None = None, file_service: Any = None) -> ImageSet:
        """
        Read a Pinnacle ImageSet header file and create an ImageSet model.

        This method reads the header file for an ImageSet, parses its contents, and loads
        the associated ImageInfoList from the corresponding ImageInfo file.

        Args:
            image_header_path: Path to ImageSet_# file (the .header extension is optional)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Raises:
            FileNotFoundError: If the header file does not exist
            ValueError: If the image index is not specified and cannot be inferred

        Example:
            >>> image_set = ImageSetReader.read_header("/path/to/Patient_0/ImageSet_0.header")
            >>> image_set = ImageSetReader.read_header("/path/to/Patient_0/ImageSet_0")
            >>> image_set = ImageSetReader.read_header("/path/to/Patient_0", image_index=0)
        """
        # Extract the file path and name
        if image_header_path.endswith(".header"):
            file_path, file_name = os.path.split(image_header_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_header_path)):
            file_path, file_name = os.path.split(image_header_path)
            file_name += ".header"
        else:
            if image_index is None:
                raise ValueError(
                    "The image index must either be specified in the image_header_path or in the image_index argument"
                )
            file_path, file_name = image_header_path, f"ImageSet_{image_index}.header"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"ImageSet header file not found: {file_path}/{file_name}")

            with file_service.open_file(file_path, file_name, "r") as f:
                image_set = ImageSetReader.parse(f.readlines())

            # Read corresponding ImageInfo file
            info_filename = file_name.replace(".header", ".ImageInfo")
            image_set.image_info_list = ImageInfoReader.read_from_path(
                os.path.join(file_path, info_filename), file_service=file_service
            )
            return image_set

        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"ImageSet header file not found: {full_path}")

        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            image_set = ImageSetReader.parse(f.readlines())

        # Read corresponding ImageInfo file
        info_file_path = full_path.replace(".header", ".ImageInfo")
        image_set.image_info_list = ImageInfoReader.read_from_path(info_file_path)
        return image_set

    @staticmethod
    def read_image_set(
        image_set_path: str,
        image_index: int | None = None,
        image_header: ImageSet | None = None,
        file_service: Any = None,
    ) -> ImageSet:
        """
        Read a Pinnacle ImageSet binary file and create an ImageSet model.

        This method reads the binary pixel data from the specified .img file, using header
        information to determine the image dimensions. If the header is not provided, it is
        loaded automatically. The pixel data is loaded as a numpy array.

        Args:
            image_set_path: Path to ImageSet_# file (the .img extension is optional)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            image_header: Optional ImageSet model containing header information
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Raises:
            FileNotFoundError: If the image set file does not exist
            ValueError: If the image index is not specified and cannot be inferred

        Example:
            >>> image_set = ImageSetReader.read_image_set("/path/to/ImageSet_0.img")
            >>> image_set = ImageSetReader.read_image_set("/path/to/ImageSet_0")
            >>> image_set = ImageSetReader.read_image_set("/path/to/Patient", image_index=0)
        """
        # Extract the file path and name
        if image_set_path.endswith(".img"):
            file_path, file_name = os.path.split(image_set_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_set_path)):
            file_path, file_name = os.path.split(image_set_path)
            file_name += ".img"
        else:
            if image_index is None:
                raise ValueError(
                    "The image index must either be specified in the image_header_path or in the image_index argument"
                )
            file_path, file_name = image_set_path, f"ImageSet_{image_index}.img"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"ImageSet file not found: {file_path}/{file_name}")

            if image_header is None:
                header_filename = file_name.replace(".img", ".header")
                header_path = os.path.join(file_path, header_filename)
                image_set = ImageSetReader.read_header(header_path, file_service=file_service)
            else:
                image_set = image_header

            z_dim = image_set.z_dim if image_set.z_dim is not None else 1
            y_dim = image_set.y_dim if image_set.y_dim is not None else 1
            x_dim = image_set.x_dim if image_set.x_dim is not None else 1

            with file_service.open_file(file_path, file_name, "rb") as f:
                binary_data = f.read()
                pixel_data = np.frombuffer(binary_data, dtype=np.uint16).reshape(z_dim, y_dim, x_dim)

            image_set.pixel_data = pixel_data
            return image_set

        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"ImageSet file not found: {full_path}")

        if image_header is None:
            header_file_path = full_path.replace(".img", ".header")
            image_set = ImageSetReader.read_header(header_file_path)
        else:
            image_set = image_header

        z_dim = image_set.z_dim if image_set.z_dim is not None else 1
        y_dim = image_set.y_dim if image_set.y_dim is not None else 1
        x_dim = image_set.x_dim if image_set.x_dim is not None else 1

        with open(full_path, "rb") as f:
            binary_data = f.read()
            pixel_data = np.frombuffer(binary_data, dtype=np.uint16).reshape(z_dim, y_dim, x_dim)

        image_set.pixel_data = pixel_data
        return image_set

    @staticmethod
    def _get_image_dimensions(
        file_path: str, file_name: str, image_header: ImageSet | None = None, file_service: Any = None
    ) -> Tuple[int, int, int]:
        """
        Efficiently extract x_dim, y_dim, z_dim from header file or cached values.

        Args:
            file_path: Directory path containing the header file
            file_name: Name of the .img file (used for cache key)
            image_header: Optional ImageSet with dimensions already loaded
            file_service: Optional file service for archive access

        Returns:
            Tuple of (x_dim, y_dim, z_dim)
        """
        # If image_header is provided, extract dimensions directly
        if image_header is not None:
            x_dim = image_header.x_dim if image_header.x_dim is not None else 1
            y_dim = image_header.y_dim if image_header.y_dim is not None else 1
            z_dim = image_header.z_dim if image_header.z_dim is not None else 1
            return (x_dim, y_dim, z_dim)

        # Create cache key from full path to .img file
        cache_key = os.path.join(file_path, file_name)

        # Check if dimensions are already cached
        if cache_key in ImageSetReader._dimension_cache:
            return ImageSetReader._dimension_cache[cache_key]

        # Need to read header file to get dimensions
        header_filename = file_name.replace(".img", ".header")

        x_dim = y_dim = z_dim = 0  # defaults

        # Use base class file service handling
        service = ImageSetReader._get_file_service(file_service, file_path)
        
        if not service.exists(file_path, header_filename):
            raise FileNotFoundError(f"ImageSet header file not found: {file_path}/{header_filename}")
        
        with service.open_file(file_path, header_filename, "r", encoding="latin1", errors="ignore") as f:
            for line in f:
                line = line.strip()
                if line.startswith("x_dim"):
                    x_dim = int(line.split("=")[1].strip().rstrip(";"))
                elif line.startswith("y_dim"):
                    y_dim = int(line.split("=")[1].strip().rstrip(";"))
                elif line.startswith("z_dim"):
                    z_dim = int(line.split("=")[1].strip().rstrip(";"))
                
                # Stop once we have all three dimensions
                if all(dim > 0 for dim in [x_dim, y_dim, z_dim]):
                    break

        # Make sure all dimensions were found
        if any(dim == 0 for dim in [x_dim, y_dim, z_dim]):
            raise ValueError(f"Failed to extract image dimensions from header: {header_filename}")

        # Cache the result
        dimensions = (x_dim, y_dim, z_dim)
        ImageSetReader._dimension_cache[cache_key] = dimensions

        return dimensions

    @staticmethod
    def read_image_slice(
        image_set_path: str,
        slice_index: int,
        image_header: ImageSet | None = None,
        image_index: int | None = None,
        file_service: Any = None,
    ) -> "npt.NDArray[np.uint16]":
        """
        Extract a single 2D slice from a 3D image set by reading only the necessary bytes.

        This method enables fast thumbnail generation without loading the entire dataset
        by calculating the byte offset and size for the specific slice and reading only
        those bytes from the file.

        Args:
            image_set_path: Path to ImageSet_# file (the .img extension is optional)
            slice_index: Zero-based index of the slice to extract from the 3D image set
            image_header: Optional ImageSet object containing metadata about the image
                         dimensions and structure. If None, the header will be automatically
                         loaded from the corresponding .header file.
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            file_service: File service object with open_file method

        Returns:
            2D numpy array representing the extracted slice with shape (y_dim, x_dim)

        Raises:
            FileNotFoundError: If the image set file or header file does not exist
            ValueError: If slice_index is out of bounds, slice_index is not an integer, or image dimensions are invalid

        Example:
            >>> # With automatic header loading
            >>> slice_data = ImageSetReader.read_image_slice("ImageSet_0.img", 50)
            >>> slice_data = ImageSetReader.read_image_slice("ImageSet_0", 50)
            >>> slice_data = ImageSetReader.read_image_slice("/path/to/Patient", 50, image_index=0)
            >>>
            >>> # With provided header (existing behavior)
            >>> header = ImageSetReader.read_header("ImageSet_0.header")
            >>> slice_data = ImageSetReader.read_image_slice("ImageSet_0.img", 50, header)
            >>> print(f"Slice shape: {slice_data.shape}")
        """
        # Extract the file path and name
        if image_set_path.endswith(".img"):
            file_path, file_name = os.path.split(image_set_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_set_path)):
            file_path, file_name = os.path.split(image_set_path)
            file_name += ".img"
        else:
            if image_index is None:
                raise ValueError(
                    "The image index must either be specified in the image_set_path or in the image_index argument"
                )
            file_path, file_name = image_set_path, f"ImageSet_{image_index}.img"

        # Get dimensions efficiently without full header parsing overhead
        x_dim, y_dim, z_dim = ImageSetReader._get_image_dimensions(file_path, file_name, image_header, file_service)

        # Validate slice index
        if slice_index < 0 or slice_index >= z_dim:
            raise ValueError(f"Slice index {slice_index} is out of bounds for image with {z_dim} slices")
        if slice_index != int(slice_index):
            raise ValueError(f"Slice index {slice_index} is not an integer")

        # Calculate byte offset and size for the specific slice
        # Each pixel is 2 bytes (np.uint16), slice size is y_dim * x_dim pixels
        bytes_per_pixel = 2  # np.uint16
        slice_size_bytes = y_dim * x_dim * bytes_per_pixel
        slice_offset_bytes = int(slice_index) * slice_size_bytes

        # Read only the bytes for the requested slice using base class file service handling
        service = ImageSetReader._get_file_service(file_service, file_path)
        
        with service.open_file(file_path, file_name, "rb") as f:
            f.seek(slice_offset_bytes)
            slice_bytes = f.read(slice_size_bytes)

        # Verify we read the expected amount of data
        if len(slice_bytes) != slice_size_bytes:
            raise ValueError(f"Expected to read {slice_size_bytes} bytes but got {len(slice_bytes)} bytes")

        # Convert bytes to numpy array and reshape to 2D slice
        slice_data = np.frombuffer(slice_bytes, dtype=np.uint16).reshape(y_dim, x_dim)

        return slice_data

    @staticmethod
    def parse(content_lines: list[str]) -> ImageSet:
        """
        Parse a Pinnacle ImageSet header content string and create an ImageSet model.

        The ImageInfoList is not parsed.

        Args:
            content_lines: Pinnacle ImageSet header content lines

        Returns:
            ImageSet model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        image_set = ImageSet(**data)
        return image_set
