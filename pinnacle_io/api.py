"""
Unified API for reading and writing Pinnacle data.

This module provides the main entry points for the pinnacle_io library,
offering a simple interface for accessing Pinnacle treatment planning data
from various sources including directories, tar files, and zip files.
"""

from pathlib import Path
from typing import TYPE_CHECKING
import numpy as np
import numpy.typing as npt

if TYPE_CHECKING:
    # Import types only during type checking
    from .models.image_set import ImageSet

from .services.base_reader_service import BaseReaderService
from .services.file_reader import FileReader
from .services.tar_file_reader import TarFileReader
from .services.zip_file_reader import ZipFileReader
from .models import (
    Institution,
    Patient,
    ImageSet,
    ImageInfo,
    Trial,
    ROI,
    Point,
    PatientSetup,
    Machine,
    Dose,
    Plan,
)


class PinnacleReader:
    """
    Recommended class for reading Pinnacle data.

    This class provides the preferred interface for reading Pinnacle treatment planning data
    from various sources including directories, tar files, and zip files. It manages the
    appropriate reader service internally based on the source type.

    Args:
        path: Path to the data source (directory, tar file, or zip file)

    Examples:
        # Initialize a Pinnacle reader
        reader = PinnacleReader("/path/to/pinnacle/data")
        institution = reader.get_institution()

        # Load patients from institution
        for patient_lite in institution.patient_lite_list:
            patient = reader.get_patient(patient_lite.patient_path)
            print(patient.last_and_first_name)
    """

    def __init__(self, path: str | Path):
        """Initialize the PinnacleReader with the given path."""
        self.path = Path(path)
        self._reader_service: BaseReaderService
        self._initialize_reader_service()

    def _initialize_reader_service(self) -> None:
        """Initialize the appropriate reader service based on path type."""
        if not self.path.exists():
            raise FileNotFoundError(f"Path not found: {self.path}")

        if self.path.is_dir():
            self._reader_service = FileReader(str(self.path))
        elif self.path.suffix.lower() in [".tar", ".gz", ".tgz"] or self.path.name.endswith(".tar.gz"):
            self._reader_service = TarFileReader(str(self.path))
        elif self.path.suffix.lower() == ".zip":
            self._reader_service = ZipFileReader(str(self.path))
        else:
            raise ValueError(f"Unsupported file format: {self.path.suffix}")

    def get_institution(self) -> Institution:
        """
        Retrieve the Institution object from the Pinnacle data source.

        The Institution object contains facility-level information including the institution name,
        location details, and a list of patient references. This is typically the root object
        in a Pinnacle data hierarchy.

        Returns:
            Institution: The institution object containing facility information and patient references.

        Raises:
            FileNotFoundError: If the Institution file is not found in the data source.
            ValueError: If the Institution file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> institution = reader.get_institution()
            >>> print(f"Institution: {institution.name}")
            >>> print(f"Number of patients: {len(institution.patient_lite_list)}")
        """
        return self._reader_service.get_institution()

    def get_patient(self, institution: Institution | int, patient: Patient | int, mount_id: int = 0) -> Patient:
        """
        Retrieve a Patient object from the Pinnacle data source.

        The Patient object contains comprehensive patient information including demographics,
        medical record details, associated image sets, and treatment plans. Patient data
        is typically organized within the Institution hierarchy.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            mount_id: Mount ID number (default: 0)

        Returns:
            Patient: The patient object containing demographics, image sets, and plans.

        Raises:
            FileNotFoundError: If the specified patient is not found.
            ValueError: If the Patient file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Load patient by IDs
            >>> patient = reader.get_patient(institution=1, patient=1)
            >>> print(f"Patient: {patient.last_and_first_name}")
            >>> print(f"MRN: {patient.medical_record_number}")
        """
        return self._reader_service.get_patient(institution, patient, mount_id)

    def get_image_header(self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, mount_id: int = 0) -> ImageSet:
        """
        Retrieve an ImageSet object excluding the medical imaging pixel data.

        The ImageSet object represents a complete medical image series (CT, MR, PET, etc.)
        including pixel data, DICOM metadata, spatial coordinates, and associated image
        information. This is the primary container for volumetric imaging data used in
        treatment planning.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number (e.g., 0 for ImageSet_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            ImageSet: The image set object containing metadata and spatial information but excluding
                     pixel data.

        Raises:
            FileNotFoundError: If the ImageSet header or image files are not found.
            ValueError: If the image index is out of range or file format is invalid.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Load specific image set header by IDs
            >>> image_set = reader.get_image_header(institution=1, patient=1, image_set=0)
            >>> print(f"Modality: {image_set.modality}")
            >>> print(f"Dimensions: {image_set.get_image_dimensions()}")
        """
        return self._reader_service.get_image_header(institution, patient, image_set, mount_id)

    def get_image_set(self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, mount_id: int = 0) -> ImageSet:
        """
        Retrieve an ImageSet object containing medical imaging data.

        The ImageSet object represents a complete medical image series (CT, MR, PET, etc.)
        including pixel data, DICOM metadata, spatial coordinates, and associated image
        information. This is the primary container for volumetric imaging data used in
        treatment planning.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number (e.g., 0 for ImageSet_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            ImageSet: The image set object containing pixel data, metadata, and spatial information.
                     The pixel_data attribute contains a numpy array with shape (z, y, x) where
                     z is the number of slices, y is the anterior-posterior dimension, and
                     x is the left-right dimension.

        Raises:
            FileNotFoundError: If the ImageSet header or image files are not found.
            ValueError: If the image index is out of range or file format is invalid.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Load specific image set by IDs
            >>> ct_images = reader.get_image_set(institution=1, patient=1, image_set=0)
            >>> print(f"Modality: {ct_images.modality}")
            >>> print(f"Dimensions: {ct_images.get_image_dimensions()}")
            >>> slice_data = ct_images.get_slice_data(50)  # Get middle slice
        """
        return self._reader_service.get_image_set(institution, patient, image_set, mount_id)

    def get_image_slice(
        self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, slice_index: int,
        image_header: "ImageSet | None" = None, mount_id: int = 0
    ) -> "npt.NDArray[np.uint16]":
        """
        Retrieve a single 2D slice from a 3D image set without loading the entire dataset.

        This method provides efficient access to individual image slices by reading only the
        necessary bytes from the image file, enabling fast thumbnail generation and slice
        preview without the memory overhead of loading complete volumetric datasets.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number (e.g., 0 for ImageSet_0)
            slice_index: Zero-based index of the slice to extract from the 3D image set.
                        Must be within the range [0, z_dim-1] where z_dim is the number
                        of slices in the image set.
            image_header: Optional ImageSet object containing metadata. If None, header will be loaded automatically.
            mount_id: Mount ID number (default: 0)

        Returns:
            numpy.ndarray: A 2D numpy array representing the extracted slice with shape (y_dim, x_dim)
                          where y_dim is the anterior-posterior dimension and x_dim is the left-right
                          dimension. The array contains pixel values as uint16 data type.

        Raises:
            FileNotFoundError: If the ImageSet header or image files are not found.
            ValueError: If the slice_index is out of bounds, image_index is invalid,
                       or file format is corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get slice from specific image set by IDs
            >>> slice_data = reader.get_image_slice(institution=1, patient=1, image_set=0, slice_index=50)
            >>> print(f"Slice shape: {slice_data.shape}")
            >>> print(f"Pixel value range: {slice_data.min()} - {slice_data.max()}")
            >>>
            >>> # Use with pre-loaded header for efficiency
            >>> header = reader.get_image_header(institution=1, patient=1, image_set=0)
            >>> slice_data = reader.get_image_slice(
            ...     institution=1,
            ...     patient=1,
            ...     image_set=0,
            ...     slice_index=50,
            ...     image_header=header
            ... )
            >>>
            >>> # Use for thumbnail generation
            >>> import matplotlib.pyplot as plt
            >>> plt.imshow(slice_data, cmap='gray')
            >>> plt.title(f"Slice {slice_index}")
        """
        return self._reader_service.get_image_slice(institution, patient, image_set, slice_index, image_header, mount_id)

    def get_image_info(self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, mount_id: int = 0) -> list[ImageInfo]:
        """
        Retrieve a list of ImageInfo objects containing per-slice metadata.

        ImageInfo objects contain detailed DICOM metadata for individual image slices,
        including slice positions, acquisition parameters, timing information, and
        DICOM identifiers. This complements the ImageSet data with slice-specific details.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number (e.g., 0 for ImageSet_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            list[ImageInfo]: A list of ImageInfo objects, one for each slice in the image set.
                           Each object contains slice-specific DICOM metadata, positioning,
                           and acquisition parameters.

        Raises:
            FileNotFoundError: If the ImageInfo file is not found.
            ValueError: If the image index is out of range or file format is invalid.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get ImageInfo for a specific image set by IDs
            >>> image_info_list = reader.get_image_info(institution=1, patient=1, image_set=0)
            >>> print(f"Number of slices: {len(image_info_list)}")
            >>>
            >>> # Access slice-specific information
            >>> first_slice = image_info_list[0]
            >>> print(f"Table position: {first_slice.table_position} mm")
            >>> print(f"Slice thickness: {first_slice.slice_thickness} mm")
            >>> print(f"DICOM file: {first_slice.dicom_file_name}")
        """
        return self._reader_service.get_image_info(institution, patient, image_set, mount_id)

    def get_trials(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> list[Trial]:
        """
        Retrieve a list of Trial objects from a treatment plan.

        Trial objects represent individual treatment delivery scenarios within a treatment plan,
        containing beam configurations, dose calculation parameters, optimization settings,
        and delivery instructions. Each trial represents a complete treatment approach.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            list[Trial]: A list of Trial objects representing different treatment scenarios.
                        Each trial contains beam parameters, dose calculation settings,
                        optimization constraints, and delivery parameters.

        Raises:
            FileNotFoundError: If the plan.Trial file is not found.
            ValueError: If the Trial file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get trials for a specific plan by IDs
            >>> trials = reader.get_trials(institution=1, patient=1, plan=0)
            >>> print(f"Number of trials: {len(trials)}")
            >>>
            >>> # Access trial information
            >>> if trials:
            ...     primary_trial = trials[0]
            ...     print(f"Trial name: {primary_trial.name}")
            ...     print(f"Number of beams: {len(primary_trial.beam_list)}")
            ...     print(f"Prescription dose: {primary_trial.dose_per_fraction} cGy")
        """
        return self._reader_service.get_trials(institution, patient, plan, mount_id)

    def get_rois(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> list[ROI]:
        """
        Retrieve a list of Region of Interest (ROI) objects from a treatment plan.

        ROI objects represent anatomical structures, target volumes, and organs at risk
        that have been contoured for treatment planning. Each ROI contains geometric
        contour data, volumetric properties, and clinical classification information.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            list[ROI]: A list of ROI objects representing contoured anatomical structures.
                      Each ROI contains contour data, volume calculations, statistical
                      properties, and clinical classification information.

        Raises:
            FileNotFoundError: If the plan.roi file is not found.
            ValueError: If the ROI file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get ROIs for a specific plan by IDs
            >>> rois = reader.get_rois(institution=1, patient=1, plan=0)
            >>> print(f"Number of ROIs: {len(rois)}")
            >>>
            >>> # Access ROI information
            >>> for roi in rois:
            ...     print(f"ROI: {roi.name} ({roi.type})")
            ...     print(f"Volume: {roi.volume:.2f} cm³")
            ...     print(f"Color: RGB({roi.color_red}, {roi.color_green}, {roi.color_blue})")
        """
        return self._reader_service.get_rois(institution, patient, plan, mount_id)

    def get_points(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> list[Point]:
        """
        Retrieve a list of Point objects from a treatment plan.

        Point objects represent discrete spatial locations within the treatment planning
        coordinate system, including reference points, fiducial markers, isocenter
        locations, and dose calculation points. These points serve various clinical
        and technical purposes in treatment planning.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            list[Point]: A list of Point objects representing spatial reference locations.
                        Each point contains 3D coordinates, clinical classification,
                        and associated metadata for treatment planning reference.

        Raises:
            FileNotFoundError: If the plan.Points file is not found.
            ValueError: If the Points file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get points for a specific plan by IDs
            >>> points = reader.get_points(institution=1, patient=1, plan=0)
            >>> print(f"Number of points: {len(points)}")
            >>>
            >>> # Access point information
            >>> for point in points:
            ...     print(f"Point: {point.name}")
            ...     print(f"Coordinates: ({point.x:.1f}, {point.y:.1f}, {point.z:.1f}) mm")
            ...     print(f"Type: {point.type}")
        """
        return self._reader_service.get_points(institution, patient, plan, mount_id)

    def get_patient_setup(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> PatientSetup:
        """
        Retrieve the PatientSetup object from a treatment plan.

        The PatientSetup object contains patient positioning and immobilization information
        used for treatment delivery, including patient orientation, support structures,
        positioning aids, and setup verification parameters.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            PatientSetup: The patient setup object containing positioning and immobilization data.
                         Includes patient orientation, support equipment, positioning aids,
                         and verification parameters for accurate treatment delivery.

        Raises:
            FileNotFoundError: If the plan.PatientSetup file is not found.
            ValueError: If the PatientSetup file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get patient setup for a specific plan by IDs
            >>> setup = reader.get_patient_setup(institution=1, patient=1, plan=0)
            >>> print(f"Patient position: {setup.patient_position}")
            >>> print(f"Head support: {setup.head_support}")
            >>> print(f"Setup technique: {setup.setup_technique}")
            >>>
            >>> # Access positioning tolerances
            >>> print(f"Position tolerance: ±{setup.position_tolerance} mm")
        """
        return self._reader_service.get_patient_setup(institution, patient, plan, mount_id)

    def get_machines(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> list[Machine]:
        """
        Retrieve a list of Machine objects from a treatment plan.

        Machine objects represent linear accelerators and their configuration parameters
        used for treatment delivery, including beam energies, field sizes, dose rates,
        multi-leaf collimator (MLC) specifications, and safety parameters.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            list[Machine]: A list of Machine objects representing treatment delivery equipment.
                          Each machine contains energy specifications, field parameters,
                          MLC configuration, dose rate settings, and safety constraints.

        Raises:
            FileNotFoundError: If the plan.Pinnacle.Machines file is not found.
            ValueError: If the Machines file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get machines for a specific plan by IDs
            >>> machines = reader.get_machines(institution=1, patient=1, plan=0)
            >>> print(f"Number of machines: {len(machines)}")
            >>>
            >>> # Access machine information
            >>> if machines:
            ...     linac = machines[0]
            ...     print(f"Machine: {linac.name}")
            ...     print(f"Manufacturer: {linac.manufacturer}")
            ...     print(f"Available energies: {len(linac.photon_energy_list)} MV")
            ...     print(f"Max field size: {linac.max_field_x} x {linac.max_field_y} cm²")
        """
        return self._reader_service.get_machines(institution, patient, plan, mount_id)

    def get_dose(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, trial: Trial, mount_id: int = 0) -> Dose:
        """
        Retrieve the Dose object from a treatment plan.

        The Dose object contains the three-dimensional dose distribution calculated for
        a specific trial, including dose grid data, statistical parameters, and spatial
        coordinate information. This represents the calculated radiation dose throughout
        the patient volume.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            trial: Trial object to specify which trial's dose to retrieve.
            mount_id: Mount ID number (default: 0)

        Returns:
            Dose: The dose object containing 3D dose distribution data, statistical parameters,
                 and spatial coordinate information. The dose_data attribute contains a numpy
                 array with the dose values in cGy for each voxel in the calculation grid.

        Raises:
            FileNotFoundError: If the dose binary files are not found.
            ValueError: If the dose files are corrupted or the trial is not found.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get trials first to select which trial's dose to load
            >>> trials = reader.get_trials(institution=1, patient_id=1, plan_id=0)
            >>>
            >>> # Get dose for a specific trial by IDs
            >>> if trials:
            ...     dose = reader.get_dose(institution=1, patient=1, plan=0, trial=trials[0])
            ...     print(f"Dose grid dimensions: {dose.x_dim} x {dose.y_dim} x {dose.z_dim}")
            ...     print(f"Dose grid spacing: {dose.x_pixdim:.2f} mm")
            ...     print(f"Maximum dose: {dose.maximum_dose:.1f} cGy")
        """
        return self._reader_service.get_dose(institution, patient, plan, trial, mount_id)
