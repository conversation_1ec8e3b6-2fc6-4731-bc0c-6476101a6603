from abc import ABC, abstractmethod
import numpy as np
import numpy.typing as npt
from typing import Any, List, IO
from pinnacle_io.models import (
    Dose,
    ImageInfo,
    ImageSet,
    Institution,
    Machine,
    Patient,
    PatientSetup,
    Plan,
    Point,
    ROI,
    Trial,
)
from pinnacle_io.readers import (
    <PERSON><PERSON>Reader,
    ImageInfoReader,
    ImageSetReader,
    InstitutionReader,
    MachineReader,
    PatientReader,
    PatientSetupReader,
    PointReader,
    ROIReader,
    TrialReader,
)


class BaseReaderService(ABC):
    """
    Abstract base class for unified Pinnacle IO services.

    Provides a uniform interface for accessing Pinnacle data objects from various sources
    including directories, tar files, zip files, and potentially in-memory archives.

    This service abstraction enables the same code to work with different data sources
    without needing to know the underlying storage mechanism. All concrete implementations
    must provide file access methods (open_file, exists) and inherit the standardized
    get_* methods for reading specific Pinnacle data types.

    Supported Data Types:
        - Institution: Root-level institution information
        - Patient: Patient demographic and medical information
        - ImageSet: Medical imaging data sets
        - ImageInfo: Metadata about imaging data
        - Trial: Treatment trial definitions
        - ROI: Regions of interest (treatment volumes)
        - Point: Treatment planning point data
        - PatientSetup: Patient positioning information
        - Machine: Treatment delivery machine configurations
        - Dose: Dose distribution data from treatment calculations

    Usage:
        reader_service = FileReader("/path/to/data")
        institution = reader_service.get_institution()

        reader_service = TarFileReader("/path/to/data.tar.gz")
        patient = reader_service.get_patient()
    """

    @abstractmethod
    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        """
        Open a file-like object for the given filepath and filename from the underlying source.
        Args:
            filepath: The directory path or full file path within the source.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode (e.g., 'r', 'rb').
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the source.
        """
        pass

    @abstractmethod
    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the underlying source.
        Args:
            filepath: The directory path or full file path within the source.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        pass

    def get_institution(self) -> Institution:
        """
        Retrieve an Institution object from the source.

        Returns:
            Institution object using the file service abstraction.
        """
        return InstitutionReader.read(institution_path="", file_service=self)

    def get_patient(self, institution: Institution | int, patient: Patient | int, mount_id: int = 0) -> Patient:
        """
        Retrieve a Patient object from the source using institution and patient IDs.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            mount_id: Mount ID number (default: 0)

        Returns:
            Patient object using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        return PatientReader.read_from_ids(institution_id, patient_id, mount_id, file_service=self)

    def get_image_header(
        self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, mount_id: int = 0
    ) -> ImageSet:
        """
        Retrieve an ImageSet object from the source without loading the pixel data.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number
            mount_id: Mount ID number (default: 0)

        Returns:
            ImageSet object (minus the pixel data) using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract image set ID from ImageSet model or use integer directly
        if isinstance(image_set, ImageSet):
            if image_set.image_set_id is None:
                raise ValueError("ImageSet model has no image_set_id")
            image_set_id = image_set.image_set_id
        else:
            image_set_id = image_set

        # Use read_header directly for header-only loading
        image_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"
        return ImageSetReader.read_header(image_path, image_index=image_set_id, file_service=self)

    def get_image_set(
        self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, mount_id: int = 0
    ) -> ImageSet:
        """
        Retrieve an ImageSet object from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number
            mount_id: Mount ID number (default: 0)

        Returns:
            ImageSet object using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract image set ID from ImageSet model or use integer directly
        if isinstance(image_set, ImageSet):
            if image_set.image_set_id is None:
                raise ValueError("ImageSet model has no image_set_id")
            image_set_id = image_set.image_set_id
        else:
            image_set_id = image_set

        return ImageSetReader.read_from_ids(institution_id, patient_id, image_set_id, mount_id, file_service=self)

    def get_image_slice(
        self,
        institution: Institution | int,
        patient: Patient | int,
        image_set: ImageSet | int,
        slice_index: int,
        image_header: "ImageSet | None" = None,
        mount_id: int = 0,
    ) -> "npt.NDArray[np.uint16]":
        """
        Retrieve a single 2D slice from an ImageSet without loading the entire dataset.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number
            slice_index: Index of the slice to retrieve
            image_header: Optional ImageSet object containing metadata. If None, header will be loaded automatically.
            mount_id: Mount ID number (default: 0)

        Returns:
            2D numpy array representing the extracted slice using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract image set ID from ImageSet model or use integer directly
        if isinstance(image_set, ImageSet):
            if image_set.image_set_id is None:
                raise ValueError("ImageSet model has no image_set_id")
            image_set_id = image_set.image_set_id
        else:
            image_set_id = image_set

        # Construct path for ImageSetReader
        image_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}"
        return ImageSetReader.read_image_slice(image_path, slice_index, image_header, image_set_id, file_service=self)

    def get_image_info(
        self, institution: Institution | int, patient: Patient | int, image_set: ImageSet | int, mount_id: int = 0
    ) -> list[ImageInfo]:
        """
        Retrieve a list of ImageInfo objects from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            image_set: ImageSet model object or image set ID number
            mount_id: Mount ID number (default: 0)

        Returns:
            List of ImageInfo objects using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract image set ID from ImageSet model or use integer directly
        if isinstance(image_set, ImageSet):
            if image_set.image_set_id is None:
                raise ValueError("ImageSet model has no image_set_id")
            image_set_id = image_set.image_set_id
        else:
            image_set_id = image_set

        return ImageInfoReader.read_from_ids(institution_id, patient_id, image_set_id, mount_id, file_service=self)

    def get_trials(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> List[Trial]:
        """
        Retrieve a list of Trial objects from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            List of Trial objects using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        return TrialReader.read_from_ids(institution_id, patient_id, plan_id, mount_id, file_service=self)

    def get_rois(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> List[ROI]:
        """
        Retrieve a list of ROI objects from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            List of ROI objects using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        return ROIReader.read_from_ids(institution_id, patient_id, plan_id, mount_id, file_service=self)

    def get_points(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> List[Point]:
        """
        Retrieve a list of Point objects from the source.

        Args:
            institution: Institution model object or institution ID number
            patient_id: Patient ID number
            plan_id: Plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            List of Point objects using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        return PointReader.read_from_ids(institution_id, patient_id, plan_id, mount_id, file_service=self)

    def get_patient_setup(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> PatientSetup:
        """
        Retrieve a PatientSetup object from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            PatientSetup object using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        return PatientSetupReader.read_from_ids(institution_id, patient_id, plan_id, mount_id, file_service=self)

    def get_machines(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> List[Machine]:
        """
        Retrieve a list of Machine objects from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            List of Machine objects using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        return MachineReader.read_from_ids(institution_id, patient_id, plan_id, mount_id, file_service=self)

    def get_dose(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, trial: Trial, mount_id: int = 0) -> Dose:
        """
        Retrieve a Dose object from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            trial: Specify a Trial object for which to retrieve the dose.
            mount_id: Mount ID number (default: 0)

        Returns:
            Dose object using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        return DoseReader.read_from_ids(institution_id, patient_id, plan_id, trial, mount_id, file_service=self)

    def get_binary_dose(self, institution: Institution | int, patient: Patient | int, plan: Plan | int, mount_id: int = 0) -> npt.NDArray[np.float32]:
        """
        Retrieve a binary dose array from the source.

        Args:
            institution: Institution model object or institution ID number
            patient: Patient model object or patient ID number
            plan: Plan model object or plan ID number (e.g., 0 for Plan_0)
            mount_id: Mount ID number (default: 0)

        Returns:
            Binary dose array using the file service abstraction.
        """
        # Extract institution ID from Institution model or use integer directly
        if isinstance(institution, Institution):
            if institution.institution_id is None:
                raise ValueError("Institution model has no institution_id")
            institution_id = institution.institution_id
        else:
            institution_id = institution

        # Extract patient ID from Patient model or use integer directly
        if isinstance(patient, Patient):
            if patient.patient_id is None:
                raise ValueError("Patient model has no patient_id")
            patient_id = patient.patient_id
        else:
            patient_id = patient

        # Extract plan ID from Plan model or use integer directly
        if isinstance(plan, Plan):
            if plan.plan_id is None:
                raise ValueError("Plan model has no plan_id")
            plan_id = plan.plan_id
        else:
            plan_id = plan

        # Construct path for binary dose file
        plan_path = f"Institution_{institution_id}/Mount_{mount_id}/Patient_{patient_id}/Plan_{plan_id}"
        with self.open_file(plan_path, "plan.Trial.binary") as f:
            return DoseReader.read_binary_dose(f)
